// Service Worker for Update Detection
const CACHE_NAME = "app-cache-v1";
const VERSION = "{{BUILD_ID}}"; // This will be replaced during build

self.addEventListener("install", () => {});

self.addEventListener("activate", (event) => {
  event.waitUntil(self.clients.claim());
});

self.addEventListener("message", (event) => {
  if (event.data && event.data.type === "SKIP_WAITING") {
    self.skipWaiting();
  }
});
