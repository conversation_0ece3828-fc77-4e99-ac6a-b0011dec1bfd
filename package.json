{"name": "next-one-piece-cm", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@ant-design/colors": "^8.0.0", "@emotion/react": "^11.11.1", "@hookform/resolvers": "^5.0.1", "@mantine/code-highlight": "^7.16.2", "@mantine/core": "^7.16.2", "@mantine/dropzone": "^7.16.2", "@mantine/form": "^7.16.2", "@mantine/hooks": "^7.16.2", "@mantine/notifications": "^7.16.2", "@paddle/paddle-js": "^1.0.3", "@paddle/paddle-node-sdk": "^2.7.0", "@paypal/react-paypal-js": "^8.1.3", "@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dismissable-layer": "^1.1.9", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-hover-card": "^1.1.13", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-switch": "^1.2.4", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-tooltip": "^1.2.6", "@react-hook/resize-observer": "^1.2.6", "@reduxjs/toolkit": "^1.9.7", "@sentry/nextjs": "^9.12.0", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.52.0", "@tabler/icons-react": "^2.40.0", "@tiptap/core": "^2.11.5", "@tiptap/extension-bold": "^2.11.5", "@tiptap/extension-bubble-menu": "^2.11.5", "@tiptap/extension-bullet-list": "^2.12.0", "@tiptap/extension-color": "^2.11.5", "@tiptap/extension-highlight": "^2.11.5", "@tiptap/extension-italic": "^2.11.5", "@tiptap/extension-link": "^2.1.12", "@tiptap/extension-paragraph": "^2.1.12", "@tiptap/extension-subscript": "^2.1.12", "@tiptap/extension-superscript": "^2.1.12", "@tiptap/extension-text-align": "^2.11.5", "@tiptap/extension-text-style": "^2.11.5", "@tiptap/extension-underline": "^2.11.5", "@tiptap/pm": "^2.11.5", "@tiptap/react": "^2.11.5", "@tiptap/starter-kit": "^2.11.5", "@vercel/blob": "^0.22.1", "antd": "^5.25.1", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "cors": "^2.8.5", "express": "^4.18.2", "formidable": "^3.5.1", "geist": "^1.1.0", "import-in-the-middle": "^1.13.1", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.487.0", "next": "^15.5.2", "next-redux-wrapper": "^8.1.0", "next-themes": "^0.4.6", "p-queue": "^8.1.0", "postcss": "8.5.3", "posthog-js": "^1.235.5", "posthog-node": "^4.11.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-easy-crop": "^5.0.5", "react-hook-form": "^7.56.2", "react-redux": "^8.1.3", "react-svg": "^16.3.0", "redux-thunk": "^2.4.2", "require-in-the-middle": "^7.5.0", "server-only": "^0.0.1", "sharp": "^0.33.5", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "typescript": "5.2.2", "vaul": "^1.1.2", "vercel": "^41.6.1", "zod": "^3.24.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/cors": "^2.8.17", "@types/formidable": "^3.4.5", "@types/jsonwebtoken": "^9.0.5", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.6", "eslint-config-prettier": "^10.1.5", "prettier": "^3.0.3", "prettier-plugin-tailwindcss": "^0.6.11", "supabase": "^2.31.8", "tailwindcss": "^4", "tw-animate-css": "^1.2.9"}, "pnpm": {"overrides": {"path-to-regexp@<0.1.12": ">=0.1.12", "@babel/runtime@<7.26.10": ">=7.26.10", "@babel/helpers@<7.26.10": ">=7.26.10", "next@>=15.0.0 <15.2.3": ">=15.2.3", "next@=15.2.3": ">=15.2.4", "undici@>=4.5.0 <5.28.5": ">=5.28.5", "esbuild@<=0.24.2": ">=0.25.0", "formidable@>=3.1.1-canary.20211030 <3.5.3": ">=3.5.3", "undici@<5.29.0": ">=5.29.0", "brace-expansion@>=1.0.0 <=1.1.11": ">=1.1.12", "brace-expansion@>=2.0.0 <=2.0.1": ">=2.0.2", "form-data@>=4.0.0 <4.0.4": ">=4.0.4", "@eslint/plugin-kit@<0.3.4": ">=0.3.4", "linkifyjs@<4.3.2": ">=4.3.2"}}}