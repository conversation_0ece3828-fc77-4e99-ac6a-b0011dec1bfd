"use client";
import { useGetStoreState } from "@/helpers/useGetStoreState";

import { useRef } from "react";
import { useGetResponsiveFontsizeInPx } from "@/hooks/useGetResponsiveFontsizeInPx";
import { getPrintReadySizeNumber } from "@/app/helpers/getPrintReadySize";
import { replaceSpacesInTextContent } from "@/utils/htmlUtils";

export default function CardTriggerTextNew() {
  const printReady = useGetStoreState("printReady");
  const ability = useGetStoreState("triggerText") as string;
  const triggerTextFontSize = useGetStoreState("triggerTextFontSize") as number;
  const target = useRef(null);
  const fontsize = useGetResponsiveFontsizeInPx({
    useTriggerTextSizeState: true,
  });
  return (
    <div
      ref={target}
      dangerouslySetInnerHTML={{ __html: replaceSpacesInTextContent(ability) }}
      id={"trigger-text"}
      style={{
        fontSize: `${getPrintReadySizeNumber(fontsize, printReady)}px`,
        lineHeight: printReady
          ? `${getPrintReadySizeNumber(1.8084 * (19.8 / triggerTextFontSize), printReady)}em`
          : `${1.6484 * (19.8 / triggerTextFontSize)}em`,
        left: "1.75%",
        width: "98%",
        zIndex: 1,
        letterSpacing: `-0.025em`,
      }}
      className="trigger-text font-geologica relative text-left font-light break-words"
    ></div>
  );
}
